package com.qbook.insight.analyzer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qbook.insight.analyzer.service.AnaResultService;
import com.qbook.insight.analyzer.util.UniversalResultBuilder;
import com.qbook.insight.common.constant.ReportStage;
import com.qbook.insight.common.domain.report.result.BasicInfo;
import com.qbook.insight.common.domain.report.result.LegalPerson;
import com.qbook.insight.common.domain.report.result.Result;
import com.qbook.insight.common.entity.IndicatorConfig;
import com.qbook.insight.common.entity.Report;
import com.qbook.insight.common.entity.ReportResult;
import com.qbook.insight.common.mapper.CorpMapper;
import com.qbook.insight.common.mapper.IndicatorConfigMapper;
import com.qbook.insight.common.mapper.ReportMapper;
import com.qbook.insight.common.mapper.ReportResultMapper;
import com.qbook.insight.common.util.IndicatorCalculator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报告结果分析实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnaResultServiceImpl implements AnaResultService {

  @Resource private IndicatorConfigMapper indicatorConfigMapper;
  @Resource private IndicatorCalculator indicatorCalculator;
  @Resource private ReportResultMapper reportResultMapper;
  @Resource private CorpMapper corpMapper;
  @Resource private ReportMapper reportMapper;

  @Override
  @DSTransactional
  public void anaReportResult(Report report) {
    try {
      List<IndicatorConfig> configList = indicatorConfigMapper.selectConfigList();

      // 获取企业基本信息
      Map<String, Object> corpInfo = corpMapper.selectCorpInfoById(report.getCorpId());
      BasicInfo basicInfo = BeanUtil.toBean(corpInfo, BasicInfo.class);
      LegalPerson legalPerson = BeanUtil.toBean(corpInfo, LegalPerson.class);
      basicInfo.setLegalPerson(legalPerson);

      Map<String, Object> map = new HashMap<>();
      map.put("tax_id", basicInfo.getCreditCode());

      // 获取所有指标计算结果 - 税负分析
      Map<String, Object> resultMap =
          configList.stream()
              .map(config -> indicatorCalculator.calculate(config, map))
              .flatMap(m -> m.entrySet().stream())
              .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

      log.debug("指标计算结果:{}", JSONUtil.toJsonStr(resultMap));

      Result result = UniversalResultBuilder.buildResult(resultMap);
      result.setBasicInfo(basicInfo);

      // TODO 其他章节分析结果

      ReportResult reportResult =
          new ReportResult()
              .setReportId(report.getId())
              .setUserId(report.getUserId())
              .setResult(JSONUtil.toJsonStr(result));

      log.info("报告结果生成完成: {}", JSONUtil.toJsonStr(reportResult));



//      reportResultMapper.insert(reportResult);

      LambdaUpdateWrapper<Report> updateWrapper = new LambdaUpdateWrapper<>();
      updateWrapper.set(Report::getStage, ReportStage.ANALYZED).eq(Report::getId, report.getId());
      reportMapper.update(null, updateWrapper);

    } catch (Exception e) {
      log.error("分析报告结果失败:", e);
    }
  }
}
