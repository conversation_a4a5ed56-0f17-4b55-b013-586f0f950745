package com.qbook.insight.analyzer.util;

import cn.hutool.core.bean.BeanPath;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.qbook.insight.common.domain.report.result.Result;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 简化版通用 Result 构造器
 */
@Slf4j
public class UniversalResultBuilder {

  public static Result buildResult(Map<String, Object> resultMap) {
    if (CollUtil.isEmpty(resultMap)) {
      return new Result();
    }

    Result result = new Result();

    try {
      PathAnalysisResult analysis = analyzeFieldPaths(Result.class, resultMap.keySet());

      analysis.getSimpleFields().forEach(fieldPath -> {
        Object value = resultMap.get(fieldPath);
        try {
          new BeanPath(fieldPath).set(result, value);
        } catch (Exception e) {
          log.warn("设置简单字段失败: {}, 值: {}", fieldPath, value, e);
        }
      });

      for (Map.Entry<String, Set<String>> entry : analysis.getListFieldGroups().entrySet()) {
        String listPath = entry.getKey();
        List<Object> listData = mergeListData(Result.class, listPath, entry.getValue(), resultMap);
        new BeanPath(listPath).set(result, listData);
      }

    } catch (Exception e) {
      log.error("构造Result对象失败", e);
    }

    return result;
  }

  /** 分析字段路径结构 */
  private static PathAnalysisResult analyzeFieldPaths(Class<?> rootClass, Set<String> fieldPaths) {
    PathAnalysisResult result = new PathAnalysisResult();

    for (String path : fieldPaths) {
      String listFieldPath = findListFieldPath(rootClass, path);
      if (listFieldPath != null) {
        result.addListField(listFieldPath, path);
      } else {
        result.addSimpleField(path);
      }
    }

    return result;
  }

  /** 查找路径中包含的 List 字段路径 */
  private static String findListFieldPath(Class<?> rootClass, String fieldPath) {
    String[] parts = fieldPath.split("\\.");
    Class<?> current = rootClass;
    StringBuilder pathBuilder = new StringBuilder();

    for (String part : parts) {
      if (pathBuilder.length() > 0) pathBuilder.append(".");
      pathBuilder.append(part);

      try {
        Class<?> finalCurrent = current;
        Optional<Field> fieldOpt = Arrays.stream(ReflectUtil.getFields(finalCurrent))
            .filter(f -> f.getName().equals(part)).findFirst();
        if (fieldOpt.isEmpty()) return null;

        Class<?> type = fieldOpt.get().getType();
        if (List.class.isAssignableFrom(type)) {
          return pathBuilder.toString();
        }

        current = type;
      } catch (Exception e) {
        return null;
      }
    }

    return null;
  }

  /** 合并 List 类型字段数据 */
  private static List<Object> mergeListData(
      Class<?> rootClass,
      String listFieldPath,
      Set<String> relatedPaths,
      Map<String, Object> resultMap) {

    Class<?> elementType = getListElementType(rootClass, listFieldPath);
    if (elementType == null) {
      log.warn("无法识别 List 元素类型: {}", listFieldPath);
      return Collections.emptyList();
    }

    Map<Integer, Map<String, Object>> yearDataMap = new HashMap<>();

    for (String path : relatedPaths) {
      Object data = resultMap.get(path);
      if (!(data instanceof List)) continue;

      List<Map<String, Object>> list = (List<Map<String, Object>>) data;

      for (Map<String, Object> item : list) {
        Integer year = Convert.toInt(item.get("year"));
        if (year == null) continue;

        yearDataMap.computeIfAbsent(year, k -> new HashMap<>());
        Map<String, Object> yearData = yearDataMap.get(year);

        if (path.equals(listFieldPath)) {
          yearData.putAll(item);
        } else {
          String subPath = path.length() > listFieldPath.length()
              ? path.substring(listFieldPath.length() + 1)
              : null;
          if (subPath != null) {
            Object val = item.get(subPath.substring(subPath.lastIndexOf('.') + 1));

            // 确保年份信息被保留
            if (!yearData.containsKey("year")) {
              yearData.put("year", year);
            }

            setValueInMap(yearData, subPath, val);
          }
        }
      }
    }

    return yearDataMap.keySet().stream().sorted()
        .map(year -> BeanUtil.fillBeanWithMap(yearDataMap.get(year), ReflectUtil.newInstance(elementType), true))
        .collect(Collectors.toList());
  }

  /** 提取 List 元素类型 */
  private static Class<?> getListElementType(Class<?> rootClass, String listPath) {
    try {
      String[] parts = listPath.split("\\.");
      Class<?> current = rootClass;

      for (String part : parts) {
        Field field = ReflectUtil.getField(current, part);
        if (field == null) return null;

        if (List.class.isAssignableFrom(field.getType())) {
          Type genType = field.getGenericType();
          if (genType instanceof ParameterizedType paramType) {
            return (Class<?>) paramType.getActualTypeArguments()[0];
          }
        }

        current = field.getType();
      }
    } catch (Exception e) {
      log.warn("获取 List 元素类型失败: {}", listPath, e);
    }

    return null;
  }

  /** 在 Map 中设置嵌套字段值 */
  private static void setValueInMap(Map<String, Object> map, String path, Object value) {
    String[] parts = path.split("\\.");
    Map<String, Object> current = map;

    for (int i = 0; i < parts.length - 1; i++) {
      current = (Map<String, Object>) current.computeIfAbsent(parts[i], k -> new HashMap<>());
    }

    current.put(parts[parts.length - 1], value);
  }

  /** 在Map中设置嵌套值 */
  private static void setNestedValueInMap(Map<String, Object> map, String fieldPath, Object value) {
    String[] pathParts = fieldPath.split("\\.");
    Map<String, Object> currentMap = map;

    // 创建中间Map
    for (int i = 0; i < pathParts.length - 1; i++) {
      String part = pathParts[i];
      if (!currentMap.containsKey(part)) {
        currentMap.put(part, new HashMap<String, Object>());
      }
      currentMap = (Map<String, Object>) currentMap.get(part);
    }

    // 设置最终值
    currentMap.put(pathParts[pathParts.length - 1], value);
  }

  /** 从Map创建对象 */
  private static Object createElementFromMap(
      Class<?> elementType, Map<String, Object> dataMap) {
    try {
      Object element = ReflectUtil.newInstance(elementType);

      // 使用Hutool的BeanUtil进行属性复制
      BeanUtil.fillBeanWithMap(dataMap, element, true);

      return element;
    } catch (Exception e) {
      log.error("创建对象失败: {}", elementType.getSimpleName(), e);
      return null;
    }
  }

  /** 路径分析结果 */
  @Getter
  private static class PathAnalysisResult {
    private final Set<String> simpleFields = new HashSet<>();
    private final Map<String, Set<String>> listFieldGroups = new HashMap<>();

    public void addSimpleField(String fieldPath) {
      simpleFields.add(fieldPath);
    }

    public void addListField(String listFieldPath, String fullFieldPath) {
      listFieldGroups.computeIfAbsent(listFieldPath, k -> new HashSet<>()).add(fullFieldPath);
    }
  }
}
