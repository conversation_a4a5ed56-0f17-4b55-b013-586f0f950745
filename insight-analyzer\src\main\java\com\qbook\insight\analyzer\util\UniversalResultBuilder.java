package com.qbook.insight.analyzer.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.qbook.insight.common.domain.report.result.Result;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用Result构造器
 *
 * <AUTHOR>
 */
@Slf4j
public class UniversalResultBuilder {

  /**
   * 构造Result对象
   *
   * @param resultMap 计算结果Map，key为字段路径，value为数据
   * @return Result对象
   */
  public static Result buildResult(Map<String, Object> resultMap) {
    if (CollUtil.isEmpty(resultMap)) {
      return new Result();
    }

    Result result = new Result();

    try {
      PathAnalysisResult analysis = analyzeFieldPaths(result.getClass(), resultMap.keySet());

      processSimpleFields(result, resultMap, analysis.getSimpleFields());

      processListFields(result, resultMap, analysis.getListFieldGroups());

      log.debug(
          "Result对象构造完成，处理了{}个简单字段，{}个List字段组",
          analysis.getSimpleFields().size(),
          analysis.getListFieldGroups().size());

    } catch (Exception e) {
      log.error("构造Result对象失败", e);
    }

    return result;
  }

  /** 分析字段路径结构 */
  private static PathAnalysisResult analyzeFieldPaths(Class<?> rootClass, Set<String> fieldPaths) {
    PathAnalysisResult result = new PathAnalysisResult();

    for (String fieldPath : fieldPaths) {
      try {
        String listFieldPath = findListFieldPath(rootClass, fieldPath);
        if (listFieldPath != null) {
          // List字段，需要分组处理
          result.addListField(listFieldPath, fieldPath);
          log.debug("识别到List字段: {} -> {}", listFieldPath, fieldPath);
        } else {
          // 简单字段，直接设置
          result.addSimpleField(fieldPath);
          log.debug("识别到简单字段: {}", fieldPath);
        }
      } catch (Exception e) {
        log.warn("分析字段路径失败: {}, 错误: {}", fieldPath, e.getMessage());
      }
    }

    return result;
  }

  /** 查找字段路径中的List字段路径 返回第一个List类型字段的完整路径 */
  private static String findListFieldPath(Class<?> rootClass, String fieldPath) {
    String[] pathParts = fieldPath.split("\\.");
    Class<?> currentClass = rootClass;
    StringBuilder currentPath = new StringBuilder();

    for (String fieldName : pathParts) {
      if (currentPath.length() > 0) {
        currentPath.append(".");
      }
      currentPath.append(fieldName);

      Field field = ReflectUtil.getField(currentClass, fieldName);
      if (field == null) {
        // 字段不存在，可能是动态字段或错误路径
        break;
      }

      // 检查是否为List类型
      if (List.class.isAssignableFrom(field.getType())) {
        return currentPath.toString();
      }

      // 继续向下查找
      currentClass = field.getType();
    }

    return null;
  }

  /** 处理简单字段（非List） */
  private static void processSimpleFields(
      Result result, Map<String, Object> resultMap, Set<String> simpleFields) {
    for (String fieldPath : simpleFields) {
      try {
        Object value = resultMap.get(fieldPath);
        setNestedFieldValue(result, fieldPath, value);
        log.debug("设置简单字段成功: {} = {}", fieldPath, value);
      } catch (Exception e) {
        log.error("设置简单字段失败: {}", fieldPath, e);
      }
    }
  }

  /** 处理List字段（需要年份匹配） */
  private static void processListFields(
      Result result, Map<String, Object> resultMap, Map<String, Set<String>> listFieldGroups) {
    for (Map.Entry<String, Set<String>> entry : listFieldGroups.entrySet()) {
      String listFieldPath = entry.getKey();
      Set<String> relatedPaths = entry.getValue();

      try {
        // 合并同一List的所有数据
        List<Object> mergedList =
            mergeListData(result.getClass(), listFieldPath, relatedPaths, resultMap);

        // 设置到Result对象
        setNestedFieldValue(result, listFieldPath, mergedList);

        log.debug("处理List字段成功: {}, 生成{}个元素", listFieldPath, mergedList.size());

      } catch (Exception e) {
        log.error("处理List字段失败: {}", listFieldPath, e);
      }
    }
  }

  /** 合并List数据（按年份） */
  private static List<Object> mergeListData(
      Class<?> rootClass,
      String listFieldPath,
      Set<String> relatedPaths,
      Map<String, Object> resultMap) {

    // 1. 获取List元素类型
    Class<?> elementType = getListElementType(rootClass, listFieldPath);
    if (elementType == null) {
      log.warn("无法确定List元素类型: {}", listFieldPath);
      return new ArrayList<>();
    }

    // 2. 收集所有年份数据
    Map<Integer, Map<String, Object>> dataByYear = new HashMap<>();

    for (String path : relatedPaths) {
      Object pathData = resultMap.get(path);
      if (!(pathData instanceof List)) {
        log.warn(
            "期望List类型数据，实际类型: {}, 路径: {}",
            pathData != null ? pathData.getClass().getSimpleName() : "null",
            path);
        continue;
      }

      @SuppressWarnings("unchecked")
      List<Map<String, Object>> dataList = (List<Map<String, Object>>) pathData;

      for (Map<String, Object> dataItem : dataList) {
        Integer year = Convert.toInt(dataItem.get("year"));
        if (year == null) {
          log.debug("数据项缺少year字段，跳过: {}", dataItem);
          continue;
        }

        // 初始化年份数据
        dataByYear.computeIfAbsent(year, k -> new HashMap<>());
        Map<String, Object> yearData = dataByYear.get(year);

        if (path.equals(listFieldPath)) {
          // 主对象数据，直接合并所有字段
          yearData.putAll(dataItem);
        } else {
          // 子字段数据，按路径设置
          String subFieldPath = path.substring(listFieldPath.length() + 1);
          // 从子字段路径中提取最后一个字段名作为数据字段名
          String[] subPathParts = subFieldPath.split("\\.");
          String dataFieldName = subPathParts[subPathParts.length - 1];
          Object value = dataItem.get(dataFieldName);
          log.debug(
              "处理子字段数据: path={}, subFieldPath={}, dataFieldName={}, value={}",
              path,
              subFieldPath,
              dataFieldName,
              value);
          setNestedValueInMap(yearData, subFieldPath, value);
        }
      }
    }

    // 3. 转换为对象列表
    List<Object> result = new ArrayList<>();
    List<Integer> sortedYears = dataByYear.keySet().stream().sorted().collect(Collectors.toList());

    for (Integer year : sortedYears) {
      Map<String, Object> yearData = dataByYear.get(year);

      // 创建对象实例
      Object element = createElementFromMap(elementType, yearData);
      if (element != null) {
        result.add(element);
      }
    }

    log.debug("合并List数据完成: {}, 年份数量: {}", listFieldPath, result.size());
    return result;
  }

  /** 获取List字段的元素类型 */
  private static Class<?> getListElementType(Class<?> rootClass, String listFieldPath) {
    try {
      Field listField = getNestedField(rootClass, listFieldPath);
      if (listField == null) {
        return null;
      }

      Type genericType = listField.getGenericType();
      if (genericType instanceof ParameterizedType) {
        ParameterizedType paramType = (ParameterizedType) genericType;
        Type[] actualTypes = paramType.getActualTypeArguments();
        if (actualTypes.length > 0) {
          return (Class<?>) actualTypes[0];
        }
      }

      return null;
    } catch (Exception e) {
      log.error("获取List元素类型失败: {}", listFieldPath, e);
      return null;
    }
  }

  /** 获取嵌套字段 */
  private static Field getNestedField(Class<?> rootClass, String fieldPath) {
    String[] pathParts = fieldPath.split("\\.");
    Class<?> currentClass = rootClass;
    Field field = null;

    for (String fieldName : pathParts) {
      field = ReflectUtil.getField(currentClass, fieldName);
      if (field == null) {
        return null;
      }
      currentClass = field.getType();
    }

    return field;
  }

  /** 设置嵌套字段值 */
  private static void setNestedFieldValue(Object target, String fieldPath, Object value) {
    try {
      String[] pathParts = fieldPath.split("\\.");
      Object currentObject = target;

      // 遍历路径，创建中间对象
      for (int i = 0; i < pathParts.length - 1; i++) {
        String fieldName = pathParts[i];
        Field field = ReflectUtil.getField(currentObject.getClass(), fieldName);
        if (field == null) {
          log.warn("字段不存在: {}.{}", currentObject.getClass().getSimpleName(), fieldName);
          return;
        }

        Object fieldValue = ReflectUtil.getFieldValue(currentObject, field);
        if (fieldValue == null) {
          // 创建新对象
          Class<?> fieldType = field.getType();
          if (List.class.isAssignableFrom(fieldType)) {
            fieldValue = new ArrayList<>();
          } else {
            fieldValue = ReflectUtil.newInstance(fieldType);
          }
          ReflectUtil.setFieldValue(currentObject, field, fieldValue);
        }

        currentObject = fieldValue;
      }

      // 设置最终字段值
      String finalFieldName = pathParts[pathParts.length - 1];
      Field finalField = ReflectUtil.getField(currentObject.getClass(), finalFieldName);
      if (finalField != null) {
        Object convertedValue = Convert.convert(finalField.getType(), value);
        ReflectUtil.setFieldValue(currentObject, finalField, convertedValue);
      } else {
        log.warn("最终字段不存在: {}.{}", currentObject.getClass().getSimpleName(), finalFieldName);
      }

    } catch (Exception e) {
      log.error("设置字段值失败: {}", fieldPath, e);
    }
  }

  /** 在Map中设置嵌套值 */
  private static void setNestedValueInMap(Map<String, Object> map, String fieldPath, Object value) {
    String[] pathParts = fieldPath.split("\\.");
    Map<String, Object> currentMap = map;

    // 创建中间Map
    for (int i = 0; i < pathParts.length - 1; i++) {
      String part = pathParts[i];
      if (!currentMap.containsKey(part)) {
        currentMap.put(part, new HashMap<String, Object>());
      }
      currentMap = (Map<String, Object>) currentMap.get(part);
    }

    // 设置最终值
    currentMap.put(pathParts[pathParts.length - 1], value);
  }

  /** 从Map创建对象 */
  private static Object createElementFromMap(
      Class<?> elementType, Map<String, Object> dataMap) {
    try {
      Object element = ReflectUtil.newInstance(elementType);

      // 智能属性复制：只设置目标类中存在的字段
      for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
        String fieldName = entry.getKey();
        Object value = entry.getValue();

        // 检查目标类是否有对应的字段
        Field field = ReflectUtil.getField(elementType, fieldName);
        if (field != null) {
          // 字段存在，进行类型转换并设置值
          Object convertedValue = Convert.convert(field.getType(), value);
          ReflectUtil.setFieldValue(element, field, convertedValue);
          log.debug("设置字段: {}.{} = {}", elementType.getSimpleName(), fieldName, convertedValue);
        } else {
          // 字段不存在，跳过设置
          log.debug("跳过不存在的字段: {}.{}", elementType.getSimpleName(), fieldName);
        }
      }

      return element;
    } catch (Exception e) {
      log.error("创建对象失败: {}", elementType.getSimpleName(), e);
      return null;
    }
  }

  /** 路径分析结果 */
  @Getter
  private static class PathAnalysisResult {
    private final Set<String> simpleFields = new HashSet<>();
    private final Map<String, Set<String>> listFieldGroups = new HashMap<>();

    public void addSimpleField(String fieldPath) {
      simpleFields.add(fieldPath);
    }

    public void addListField(String listFieldPath, String fullFieldPath) {
      listFieldGroups.computeIfAbsent(listFieldPath, k -> new HashSet<>()).add(fullFieldPath);
    }
  }
}
