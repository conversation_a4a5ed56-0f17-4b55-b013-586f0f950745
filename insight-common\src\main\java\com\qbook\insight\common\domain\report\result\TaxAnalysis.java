package com.qbook.insight.common.domain.report.result;

import com.qbook.insight.common.annotation.ResultLevel1;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 税务分析
 *
 * <AUTHOR>
 */
@Data
public class TaxAnalysis {

  @ApiModelProperty("企业所得税")
  @ResultLevel1
  private List<CorporateIncomeTax> corporateIncomeTax;

  @ApiModelProperty("增值税")
  @ResultLevel1
  private List<VAT> vat;

  @ApiModelProperty("房产税")
  @ResultLevel1
  private List<PropertyTax> propertyTax;

  @ApiModelProperty("城镇土地使用税")
  @ResultLevel1
  private List<LandUseTax> landUseTax;

  @ApiModelProperty("消费税")
  @ResultLevel1
  private List<ConsumptionTax> consumptionTax;

  @ApiModelProperty("印花税")
  @ResultLevel1
  private List<StampDuty> stampDuty;

  @ApiModelProperty("城建税")
  private List<UrbanMaintenanceTax> urbanMaintenanceTax;

  @ApiModelProperty("教育费附加及地方教育费附加")
  @ResultLevel1
  private List<EducationSurcharge> educationSurcharge;

  @ApiModelProperty("土地增值税")
  private List<LandAppreciationTax> landAppreciationTax;

  @ApiModelProperty("资源税")
  @ResultLevel1
  private List<ResourceTax> resourceTax;

  @ApiModelProperty("个人所得税")
  @ResultLevel1
  private List<IndividualIncomeTax> individualIncomeTax;

  @ApiModelProperty("社会保险金")
  @ResultLevel1
  private List<SocialInsurance> socialInsurance;
}
